#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>

@interface RedPacketManager : NSObject
@property (nonatomic, strong) UIView *currentRedPacketView;
+ (instancetype)sharedInstance;
- (void)showRedPacket;
- (void)hideRedPacket;
- (UIViewController *)topViewController;
- (CGFloat)randomAmount;
- (void)openRedPacket:(UIButton *)sender;
- (void)setupBottomViewContent:(UIView *)bottomView amount:(CGFloat)amount;
@end

@implementation RedPacketManager

+ (instancetype)sharedInstance {
    static RedPacketManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RedPacketManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 延迟3秒显示红包
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showRedPacket];
        });
    }
    return self;
}

- (CGFloat)randomAmount {
    // 生成10.00-30.00之间的随机金额，保留两位小数
    CGFloat min = 10.00;
    CGFloat max = 30.00;
    CGFloat random = min + (max - min) * ((CGFloat)arc4random() / UINT32_MAX);
    return roundf(random * 100) / 100.0; // 保留两位小数
}

- (void)showRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 创建全屏遮罩
        UIView *maskView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        maskView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
        maskView.tag = 888;
        
        // 红包主容器 - 仿微信红包样式
        UIView *redPacketContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 300, 450)];
        redPacketContainer.center = maskView.center;
        redPacketContainer.backgroundColor = [UIColor clearColor];
        [maskView addSubview:redPacketContainer];
        
        // 红包上半部分 - 红色背景
        UIView *topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 300, 280)];
        topView.backgroundColor = [UIColor colorWithRed:1.0 green:0.4 blue:0.4 alpha:1.0]; // 微信红包红色
        topView.layer.cornerRadius = 8;
        topView.layer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
        [redPacketContainer addSubview:topView];
        
        // 金币图标
        UIImageView *coinIcon = [[UIImageView alloc] initWithFrame:CGRectMake(20, 25, 24, 24)];
        coinIcon.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
        coinIcon.layer.cornerRadius = 12;
        coinIcon.layer.borderWidth = 2;
        coinIcon.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.0 alpha:1.0].CGColor;
        [topView addSubview:coinIcon];
        
        // "夸克新人红包" 文字
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(55, 20, 200, 20)];
        titleLabel.text = @"夸克新人红包";
        titleLabel.textColor = [UIColor whiteColor];
        titleLabel.font = [UIFont systemFontOfSize:14];
        [topView addSubview:titleLabel];
        
        // "大吉大利" 祝福语
        UILabel *blessLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 80, 300, 40)];
        blessLabel.text = @"大吉大利";
        blessLabel.textColor = [UIColor whiteColor];
        blessLabel.font = [UIFont boldSystemFontOfSize:28];
        blessLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:blessLabel];
        
        // 开红包按钮
        UIButton *openButton = [UIButton buttonWithType:UIButtonTypeCustom];
        openButton.frame = CGRectMake(125, 180, 50, 50);
        openButton.backgroundColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.7 alpha:1.0];
        openButton.layer.cornerRadius = 25;
        openButton.layer.borderWidth = 3;
        openButton.layer.borderColor = [UIColor whiteColor].CGColor;
        [openButton setTitle:@"开" forState:UIControlStateNormal];
        [openButton setTitleColor:[UIColor colorWithRed:0.8 green:0.4 blue:0.2 alpha:1.0] forState:UIControlStateNormal];
        openButton.titleLabel.font = [UIFont boldSystemFontOfSize:20];
        [openButton addTarget:self action:@selector(openRedPacket:) forControlEvents:UIControlEventTouchUpInside];
        [topView addSubview:openButton];
        
        // 底部提示文字
        UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 240, 300, 20)];
        tipLabel.text = @"拆红包";
        tipLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.7];
        tipLabel.font = [UIFont systemFontOfSize:12];
        tipLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:tipLabel];
        
        // 红包下半部分 - 白色背景（初始隐藏）
        UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, 280, 300, 170)];
        bottomView.backgroundColor = [UIColor whiteColor];
        bottomView.layer.cornerRadius = 8;
        bottomView.layer.maskedCorners = kCALayerMinXMaxYCorner | kCALayerMaxXMaxYCorner;
        bottomView.alpha = 0;
        [redPacketContainer addSubview:bottomView];
        
        self.currentRedPacketView = maskView;
        
        // 关联对象
        objc_setAssociatedObject(openButton, "bottomView", bottomView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        objc_setAssociatedObject(openButton, "maskView", maskView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
        
        // 添加到窗口
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:maskView];
        
        // 入场动画
        redPacketContainer.transform = CGAffineTransformMakeScale(0.3, 0.3);
        redPacketContainer.alpha = 0;
        [UIView animateWithDuration:0.5 delay:0 usingSpringWithDamping:0.7 initialSpringVelocity:0.5 options:0 animations:^{
            redPacketContainer.transform = CGAffineTransformIdentity;
            redPacketContainer.alpha = 1.0;
        } completion:nil];
    });
}

- (void)openRedPacket:(UIButton *)sender {
    UIView *bottomView = objc_getAssociatedObject(sender, "bottomView");
    UIView *maskView = objc_getAssociatedObject(sender, "maskView");
    
    // 按钮点击动画
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];
    
    // 生成随机金额
    CGFloat amount = [self randomAmount];
    
    // 显示底部视图
    [UIView animateWithDuration:0.3 animations:^{
        bottomView.alpha = 1.0;
    }];
    
    // 添加底部内容
    [self setupBottomViewContent:bottomView amount:amount];
    
    // 3秒后自动关闭
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self hideRedPacket];
    });
}

- (void)setupBottomViewContent:(UIView *)bottomView amount:(CGFloat)amount {
    // 清除之前的内容
    for (UIView *subview in bottomView.subviews) {
        [subview removeFromSuperview];
    }
    
    // 关闭按钮
    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    closeButton.frame = CGRectMake(15, 15, 30, 30);
    [closeButton setTitle:@"✕" forState:UIControlStateNormal];
    [closeButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    closeButton.titleLabel.font = [UIFont systemFontOfSize:18];
    [closeButton addTarget:self action:@selector(hideRedPacket) forControlEvents:UIControlEventTouchUpInside];
    [bottomView addSubview:closeButton];
    
    // 更多按钮
    UIButton *moreButton = [UIButton buttonWithType:UIButtonTypeCustom];
    moreButton.frame = CGRectMake(255, 15, 30, 30);
    [moreButton setTitle:@"⋯" forState:UIControlStateNormal];
    [moreButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
    moreButton.titleLabel.font = [UIFont boldSystemFontOfSize:18];
    [bottomView addSubview:moreButton];
    
    // 金币图标
    UIView *coinView = [[UIView alloc] initWithFrame:CGRectMake(135, 35, 30, 30)];
    coinView.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
    coinView.layer.cornerRadius = 15;
    coinView.layer.borderWidth = 2;
    coinView.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.0 alpha:1.0].CGColor;
    [bottomView addSubview:coinView];
    
    // "夸克新人红包" 标题
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 75, 300, 20)];
    titleLabel.text = @"夸克新人红包";
    titleLabel.textColor = [UIColor blackColor];
    titleLabel.font = [UIFont systemFontOfSize:14];
    titleLabel.textAlignment = NSTextAlignmentCenter;
    [bottomView addSubview:titleLabel];
    
    // "大吉大利" 副标题
    UILabel *subTitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 95, 300, 16)];
    subTitleLabel.text = @"大吉大利";
    subTitleLabel.textColor = [UIColor lightGrayColor];
    subTitleLabel.font = [UIFont systemFontOfSize:12];
    subTitleLabel.textAlignment = NSTextAlignmentCenter;
    [bottomView addSubview:subTitleLabel];
    
    // 金额显示
    UILabel *amountLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 120, 300, 30)];
    amountLabel.text = [NSString stringWithFormat:@"¥%.2f", amount];
    amountLabel.textColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
    amountLabel.font = [UIFont boldSystemFontOfSize:24];
    amountLabel.textAlignment = NSTextAlignmentCenter;
    [bottomView addSubview:amountLabel];
    
    // 提示文字
    UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 155, 300, 15)];
    tipLabel.text = @"已存入零钱，可直接消费";
    tipLabel.textColor = [UIColor lightGrayColor];
    tipLabel.font = [UIFont systemFontOfSize:11];
    tipLabel.textAlignment = NSTextAlignmentCenter;
    [bottomView addSubview:tipLabel];
}

- (void)hideRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView *maskView = self.currentRedPacketView;
        if (maskView) {
            [UIView animateWithDuration:0.3 animations:^{
                maskView.alpha = 0;
            } completion:^(BOOL finished) {
                [maskView removeFromSuperview];
                self.currentRedPacketView = nil;
            }];
        }
    });
}

- (UIViewController *)topViewController {
    UIViewController *rootVC = nil;
    UIWindow *window = nil;
    
    if (@available(iOS 15.0, *)) {
        for (UIWindowScene *windowScene in [[UIApplication sharedApplication] connectedScenes]) {
            if ([windowScene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *win in windowScene.windows) {
                    if (win.isKeyWindow) {
                        window = win;
                        break;
                    }
                }
                if (window) break;
            }
        }
    } else {
        for (UIWindow *win in [[UIApplication sharedApplication] windows]) {
            if (win.isKeyWindow) {
                window = win;
                break;
            }
        }
    }
    
    if (!window) {
        window = [[UIApplication sharedApplication] windows].firstObject;
    }
    
    rootVC = window.rootViewController;
    while (rootVC.presentedViewController) {
        rootVC = rootVC.presentedViewController;
    }
    
    return rootVC;
}

@end

__attribute__((constructor)) static void entry(void) {
    NSLog(@"RedPacket dylib loaded!");
    dispatch_async(dispatch_get_main_queue(), ^{
        [RedPacketManager sharedInstance];
    });
}
