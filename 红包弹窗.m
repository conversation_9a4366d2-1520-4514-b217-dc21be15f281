#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>

@interface RedPacketManager : NSObject
@property (nonatomic, strong) UIView *currentRedPacketView;
+ (instancetype)sharedInstance;
- (void)showRedPacket;
- (void)hideRedPacket;
- (UIViewController *)topViewController;
- (CGFloat)randomAmount;
- (void)openRedPacket:(UIButton *)sender;
- (void)showRedPacketResultPage:(CGFloat)amount;
@end

@implementation RedPacketManager

+ (instancetype)sharedInstance {
    static RedPacketManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RedPacketManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 延迟3秒显示红包
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showRedPacket];
        });
    }
    return self;
}

- (CGFloat)randomAmount {
    // 生成10.00-30.00之间的随机金额，保留两位小数
    CGFloat min = 10.00;
    CGFloat max = 30.00;
    CGFloat random = min + (max - min) * ((CGFloat)arc4random() / UINT32_MAX);
    return roundf(random * 100) / 100.0; // 保留两位小数
}

- (void)showRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 创建全屏遮罩背景
        UIView *maskView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        maskView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
        maskView.tag = 888;

        // 红包弹窗容器 - 按照参考图片的弹窗样式
        UIView *redPacketContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 320, 500)];
        redPacketContainer.center = maskView.center;
        redPacketContainer.backgroundColor = [UIColor clearColor];
        [maskView addSubview:redPacketContainer];

        // 红包主体 - 圆角矩形
        UIView *redPacketCard = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 320, 500)];
        redPacketCard.layer.cornerRadius = 12;
        redPacketCard.clipsToBounds = YES;

        // 创建渐变背景 - 微信红包的红色渐变
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = redPacketCard.bounds;
        gradientLayer.colors = @[
            (id)[UIColor colorWithRed:1.0 green:0.45 blue:0.45 alpha:1.0].CGColor,  // 顶部浅红
            (id)[UIColor colorWithRed:0.95 green:0.35 blue:0.35 alpha:1.0].CGColor  // 底部深红
        ];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        [redPacketCard.layer addSublayer:gradientLayer];
        [redPacketContainer addSubview:redPacketCard];

        // 标题容器 - 居中显示¥符号和文字
        UIView *titleContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 50, 320, 40)];
        titleContainer.backgroundColor = [UIColor clearColor];
        [redPacketCard addSubview:titleContainer];

        // 金币图标 - 在标题容器中居中
        UIView *coinIcon = [[UIView alloc] initWithFrame:CGRectMake(100, 8, 24, 24)];
        coinIcon.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
        coinIcon.layer.cornerRadius = 12;
        coinIcon.layer.borderWidth = 1.5;
        coinIcon.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.2 alpha:1.0].CGColor;

        // 在金币中添加 ¥ 符号
        UILabel *coinLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        coinLabel.text = @"¥";
        coinLabel.textColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.0 alpha:1.0];
        coinLabel.font = [UIFont boldSystemFontOfSize:12];
        coinLabel.textAlignment = NSTextAlignmentCenter;
        [coinIcon addSubview:coinLabel];
        [titleContainer addSubview:coinIcon];

        // "夸克的红包" 文字 - 紧跟¥符号
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(130, 8, 120, 24)];
        titleLabel.text = @"夸克的红包";
        titleLabel.textColor = [UIColor whiteColor];
        titleLabel.font = [UIFont systemFontOfSize:16];
        titleLabel.textAlignment = NSTextAlignmentLeft;
        [titleContainer addSubview:titleLabel];

        // "大吉大利" 祝福语 - 居中显示
        UILabel *blessLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 120, 320, 60)];
        blessLabel.text = @"大吉大利";
        blessLabel.textColor = [UIColor whiteColor];
        blessLabel.font = [UIFont boldSystemFontOfSize:36];
        blessLabel.textAlignment = NSTextAlignmentCenter;
        [redPacketCard addSubview:blessLabel];

        // 底部波浪装饰 - 仿照参考图片
        UIView *waveContainer = [[UIView alloc] initWithFrame:CGRectMake(0, 380, 320, 120)];
        waveContainer.backgroundColor = [UIColor clearColor];

        // 创建波浪形状
        UIBezierPath *wavePath = [UIBezierPath bezierPath];
        CGFloat waveWidth = 320;
        CGFloat waveHeight = 40;
        [wavePath moveToPoint:CGPointMake(0, waveHeight)];

        // 创建波浪曲线
        for (int i = 0; i <= waveWidth; i += 20) {
            CGFloat y = waveHeight + sin(i * M_PI / 60) * 8;
            [wavePath addLineToPoint:CGPointMake(i, y)];
        }
        [wavePath addLineToPoint:CGPointMake(waveWidth, 120)];
        [wavePath addLineToPoint:CGPointMake(0, 120)];
        [wavePath closePath];

        CAShapeLayer *waveLayer = [CAShapeLayer layer];
        waveLayer.path = wavePath.CGPath;
        waveLayer.fillColor = [UIColor colorWithRed:1.0 green:1.0 blue:0.75 alpha:1.0].CGColor; // #FFFFBF
        [waveContainer.layer addSublayer:waveLayer];
        [redPacketCard addSubview:waveContainer];

        // 开红包按钮 - 更大，位置更高
        UIButton *openButton = [UIButton buttonWithType:UIButtonTypeCustom];
        openButton.frame = CGRectMake(120, 360, 80, 80);
        openButton.backgroundColor = [UIColor colorWithRed:1.0 green:1.0 blue:0.75 alpha:1.0]; // #FFFFBF
        openButton.layer.cornerRadius = 40;
        openButton.layer.borderWidth = 4;
        openButton.layer.borderColor = [UIColor whiteColor].CGColor;
        openButton.layer.shadowColor = [UIColor blackColor].CGColor;
        openButton.layer.shadowOffset = CGSizeMake(0, 2);
        openButton.layer.shadowOpacity = 0.2;
        openButton.layer.shadowRadius = 4;
        [openButton setTitle:@"开" forState:UIControlStateNormal];
        [openButton setTitleColor:[UIColor colorWithRed:0.8 green:0.4 blue:0.2 alpha:1.0] forState:UIControlStateNormal];
        openButton.titleLabel.font = [UIFont boldSystemFontOfSize:32];
        [openButton addTarget:self action:@selector(openRedPacket:) forControlEvents:UIControlEventTouchUpInside];
        [redPacketCard addSubview:openButton];

        // 底部提示文字
        UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 460, 320, 20)];
        tipLabel.text = @"夸克红包";
        tipLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.8];
        tipLabel.font = [UIFont systemFontOfSize:13];
        tipLabel.textAlignment = NSTextAlignmentCenter;
        [redPacketCard addSubview:tipLabel];

        self.currentRedPacketView = maskView;

        // 关联对象
        objc_setAssociatedObject(openButton, "maskView", maskView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        // 添加到窗口
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:maskView];

        // 入场动画
        redPacketContainer.transform = CGAffineTransformMakeScale(0.3, 0.3);
        redPacketContainer.alpha = 0;
        [UIView animateWithDuration:0.5 delay:0 usingSpringWithDamping:0.7 initialSpringVelocity:0.5 options:0 animations:^{
            redPacketContainer.transform = CGAffineTransformIdentity;
            redPacketContainer.alpha = 1.0;
        } completion:nil];
    });
}

- (void)openRedPacket:(UIButton *)sender {
    UIView *maskView = objc_getAssociatedObject(sender, "maskView");

    // 按钮点击动画
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        } completion:^(BOOL finished) {
            // 生成随机金额
            CGFloat amount = [self randomAmount];

            // 先移除弹窗，然后显示全屏结果页面
            [UIView animateWithDuration:0.3 animations:^{
                maskView.alpha = 0;
            } completion:^(BOOL finished) {
                [maskView removeFromSuperview];
                // 切换到全屏结果页面
                [self showRedPacketResultPage:amount];
            }];
        }];
    }];
}

- (void)showRedPacketResultPage:(CGFloat)amount {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 移除当前红包页面
        [self.currentRedPacketView removeFromSuperview];

        // 创建全屏结果页面
        UIView *resultView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        resultView.tag = 999;

        // 上半部分红色背景 - 弧形设计
        UIView *topRedView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, resultView.frame.size.width, resultView.frame.size.height * 0.4)];

        // 创建弧形路径
        UIBezierPath *arcPath = [UIBezierPath bezierPath];
        [arcPath moveToPoint:CGPointMake(0, 0)];
        [arcPath addLineToPoint:CGPointMake(topRedView.frame.size.width, 0)];
        [arcPath addLineToPoint:CGPointMake(topRedView.frame.size.width, topRedView.frame.size.height - 40)];

        // 添加弧形底部
        [arcPath addQuadCurveToPoint:CGPointMake(0, topRedView.frame.size.height - 40)
                        controlPoint:CGPointMake(topRedView.frame.size.width / 2, topRedView.frame.size.height + 20)];
        [arcPath closePath];

        CAShapeLayer *arcLayer = [CAShapeLayer layer];
        arcLayer.path = arcPath.CGPath;
        arcLayer.fillColor = [UIColor colorWithRed:1.0 green:0.4 blue:0.4 alpha:1.0].CGColor;
        [topRedView.layer addSublayer:arcLayer];
        [resultView addSubview:topRedView];

        // 下半部分白色背景
        UIView *bottomWhiteView = [[UIView alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 - 40, resultView.frame.size.width, resultView.frame.size.height * 0.6 + 40)];
        bottomWhiteView.backgroundColor = [UIColor whiteColor];
        [resultView addSubview:bottomWhiteView];

        // 关闭按钮 - 左上角
        UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        closeButton.frame = CGRectMake(20, 50, 30, 30);
        [closeButton setTitle:@"✕" forState:UIControlStateNormal];
        [closeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        closeButton.titleLabel.font = [UIFont systemFontOfSize:20];
        [closeButton addTarget:self action:@selector(hideRedPacket) forControlEvents:UIControlEventTouchUpInside];
        [topRedView addSubview:closeButton];

        // 更多按钮 - 右上角
        UIButton *moreButton = [UIButton buttonWithType:UIButtonTypeCustom];
        moreButton.frame = CGRectMake(resultView.frame.size.width - 50, 50, 30, 30);
        [moreButton setTitle:@"⋯" forState:UIControlStateNormal];
        [moreButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        moreButton.titleLabel.font = [UIFont boldSystemFontOfSize:20];
        [topRedView addSubview:moreButton];

        // 金币图标 - 居中
        UIView *coinView = [[UIView alloc] initWithFrame:CGRectMake((resultView.frame.size.width - 50) / 2, resultView.frame.size.height * 0.4 - 65, 50, 50)];
        coinView.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
        coinView.layer.cornerRadius = 25;
        coinView.layer.borderWidth = 3;
        coinView.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.2 alpha:1.0].CGColor;
        coinView.layer.shadowColor = [UIColor blackColor].CGColor;
        coinView.layer.shadowOffset = CGSizeMake(0, 2);
        coinView.layer.shadowOpacity = 0.2;
        coinView.layer.shadowRadius = 4;

        // 在金币中添加 ¥ 符号
        UILabel *coinLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 50, 50)];
        coinLabel.text = @"¥";
        coinLabel.textColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.0 alpha:1.0];
        coinLabel.font = [UIFont boldSystemFontOfSize:20];
        coinLabel.textAlignment = NSTextAlignmentCenter;
        [coinView addSubview:coinLabel];
        [resultView addSubview:coinView];

        // "夸克的红包" 标题
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 60, resultView.frame.size.width, 25)];
        titleLabel.text = @"夸克的红包";
        titleLabel.textColor = [UIColor blackColor];
        titleLabel.font = [UIFont systemFontOfSize:16];
        titleLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:titleLabel];

        // "大吉大利" 副标题
        UILabel *subTitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 90, resultView.frame.size.width, 20)];
        subTitleLabel.text = @"大吉大利";
        subTitleLabel.textColor = [UIColor lightGrayColor];
        subTitleLabel.font = [UIFont systemFontOfSize:14];
        subTitleLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:subTitleLabel];

        // 金额显示
        UILabel *amountLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 130, resultView.frame.size.width, 50)];
        amountLabel.text = [NSString stringWithFormat:@"¥%.2f", amount];
        amountLabel.textColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
        amountLabel.font = [UIFont boldSystemFontOfSize:36];
        amountLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:amountLabel];

        // 提示文字 - 按照图片样式
        UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 190, resultView.frame.size.width, 20)];
        tipLabel.text = @"已存入我的钱包-零钱，去查看 >";
        tipLabel.textColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.2 alpha:1.0]; // 金色
        tipLabel.font = [UIFont systemFontOfSize:13];
        tipLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:tipLabel];



        self.currentRedPacketView = resultView;

        // 添加到窗口
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:resultView];

        // 入场动画
        resultView.alpha = 0;
        [UIView animateWithDuration:0.4 animations:^{
            resultView.alpha = 1.0;
        }];
    });
}

- (void)hideRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView *maskView = self.currentRedPacketView;
        if (maskView) {
            [UIView animateWithDuration:0.3 animations:^{
                maskView.alpha = 0;
            } completion:^(BOOL finished) {
                [maskView removeFromSuperview];
                self.currentRedPacketView = nil;
            }];
        }
    });
}

- (UIViewController *)topViewController {
    UIViewController *rootVC = nil;
    UIWindow *window = nil;
    
    if (@available(iOS 15.0, *)) {
        for (UIWindowScene *windowScene in [[UIApplication sharedApplication] connectedScenes]) {
            if ([windowScene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *win in windowScene.windows) {
                    if (win.isKeyWindow) {
                        window = win;
                        break;
                    }
                }
                if (window) break;
            }
        }
    } else {
        for (UIWindow *win in [[UIApplication sharedApplication] windows]) {
            if (win.isKeyWindow) {
                window = win;
                break;
            }
        }
    }
    
    if (!window) {
        window = [[UIApplication sharedApplication] windows].firstObject;
    }
    
    rootVC = window.rootViewController;
    while (rootVC.presentedViewController) {
        rootVC = rootVC.presentedViewController;
    }
    
    return rootVC;
}

@end

__attribute__((constructor)) static void entry(void) {
    NSLog(@"RedPacket dylib loaded!");
    dispatch_async(dispatch_get_main_queue(), ^{
        [RedPacketManager sharedInstance];
    });
}
