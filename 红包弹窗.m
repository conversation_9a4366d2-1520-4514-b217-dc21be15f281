#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <QuartzCore/QuartzCore.h>
#import <objc/runtime.h>

@interface RedPacketManager : NSObject
@property (nonatomic, strong) UIView *currentRedPacketView;
+ (instancetype)sharedInstance;
- (void)showRedPacket;
- (void)hideRedPacket;
- (UIViewController *)topViewController;
- (CGFloat)randomAmount;
- (void)openRedPacket:(UIButton *)sender;
- (void)showRedPacketResultPage:(CGFloat)amount;
@end

@implementation RedPacketManager

+ (instancetype)sharedInstance {
    static RedPacketManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[RedPacketManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        // 延迟3秒显示红包
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self showRedPacket];
        });
    }
    return self;
}

- (CGFloat)randomAmount {
    // 生成10.00-30.00之间的随机金额，保留两位小数
    CGFloat min = 10.00;
    CGFloat max = 30.00;
    CGFloat random = min + (max - min) * ((CGFloat)arc4random() / UINT32_MAX);
    return roundf(random * 100) / 100.0; // 保留两位小数
}

- (void)showRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 创建全屏红包页面
        UIView *redPacketView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        redPacketView.tag = 888;

        // 红包背景 - 完全按照参考图片的样式
        UIView *redPacketBg = [[UIView alloc] initWithFrame:CGRectMake(0, 0, [UIScreen mainScreen].bounds.size.width, [UIScreen mainScreen].bounds.size.height)];

        // 创建渐变背景 - 微信红包的红色渐变
        CAGradientLayer *gradientLayer = [CAGradientLayer layer];
        gradientLayer.frame = redPacketBg.bounds;
        gradientLayer.colors = @[
            (id)[UIColor colorWithRed:1.0 green:0.45 blue:0.45 alpha:1.0].CGColor,  // 顶部浅红
            (id)[UIColor colorWithRed:0.95 green:0.35 blue:0.35 alpha:1.0].CGColor  // 底部深红
        ];
        gradientLayer.startPoint = CGPointMake(0.5, 0);
        gradientLayer.endPoint = CGPointMake(0.5, 1);
        [redPacketBg.layer addSublayer:gradientLayer];
        [redPacketView addSubview:redPacketBg];

        // 添加红包装饰图案 - 仿照参考图片的半透明图案
        UIView *decorationView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, redPacketBg.frame.size.width, redPacketBg.frame.size.height)];
        decorationView.backgroundColor = [UIColor clearColor];

        // 创建大的半透明圆形装饰
        UIView *bigCircle = [[UIView alloc] initWithFrame:CGRectMake(-100, redPacketBg.frame.size.height * 0.6, 300, 300)];
        bigCircle.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.05];
        bigCircle.layer.cornerRadius = 150;
        [decorationView addSubview:bigCircle];

        // 创建小的半透明圆形装饰
        UIView *smallCircle = [[UIView alloc] initWithFrame:CGRectMake(redPacketBg.frame.size.width - 80, 100, 120, 120)];
        smallCircle.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.03];
        smallCircle.layer.cornerRadius = 60;
        [decorationView addSubview:smallCircle];

        [redPacketBg addSubview:decorationView];

        // 金币图标 - 左上角
        UIView *coinIcon = [[UIView alloc] initWithFrame:CGRectMake(20, 60, 24, 24)];
        coinIcon.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
        coinIcon.layer.cornerRadius = 12;
        coinIcon.layer.borderWidth = 1.5;
        coinIcon.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.2 alpha:1.0].CGColor;

        // 在金币中添加 ¥ 符号
        UILabel *coinLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 24, 24)];
        coinLabel.text = @"¥";
        coinLabel.textColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.0 alpha:1.0];
        coinLabel.font = [UIFont boldSystemFontOfSize:12];
        coinLabel.textAlignment = NSTextAlignmentCenter;
        [coinIcon addSubview:coinLabel];
        [redPacketBg addSubview:coinIcon];

        // "相信光光光的红包" 文字 - 紧跟金币图标
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(55, 58, 200, 28)];
        titleLabel.text = @"相信光光光的红包";
        titleLabel.textColor = [UIColor whiteColor];
        titleLabel.font = [UIFont systemFontOfSize:15];
        titleLabel.textAlignment = NSTextAlignmentLeft;
        [redPacketBg addSubview:titleLabel];

        // "大吉大利" 祝福语 - 居中显示
        UILabel *blessLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, redPacketBg.frame.size.height * 0.25, redPacketBg.frame.size.width, 60)];
        blessLabel.text = @"大吉大利";
        blessLabel.textColor = [UIColor whiteColor];
        blessLabel.font = [UIFont boldSystemFontOfSize:36];
        blessLabel.textAlignment = NSTextAlignmentCenter;
        [redPacketBg addSubview:blessLabel];

        // 底部波浪装饰 - 仿照参考图片
        UIView *waveContainer = [[UIView alloc] initWithFrame:CGRectMake(0, redPacketBg.frame.size.height - 120, redPacketBg.frame.size.width, 120)];
        waveContainer.backgroundColor = [UIColor clearColor];

        // 创建波浪形状
        UIBezierPath *wavePath = [UIBezierPath bezierPath];
        CGFloat waveWidth = redPacketBg.frame.size.width;
        CGFloat waveHeight = 40;
        [wavePath moveToPoint:CGPointMake(0, waveHeight)];

        // 创建波浪曲线
        for (int i = 0; i <= waveWidth; i += 20) {
            CGFloat y = waveHeight + sin(i * M_PI / 60) * 8;
            [wavePath addLineToPoint:CGPointMake(i, y)];
        }
        [wavePath addLineToPoint:CGPointMake(waveWidth, 120)];
        [wavePath addLineToPoint:CGPointMake(0, 120)];
        [wavePath closePath];

        CAShapeLayer *waveLayer = [CAShapeLayer layer];
        waveLayer.path = wavePath.CGPath;
        waveLayer.fillColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.7 alpha:1.0].CGColor;
        [waveContainer.layer addSublayer:waveLayer];
        [redPacketBg addSubview:waveContainer];

        // 开红包按钮 - 在波浪上方
        UIButton *openButton = [UIButton buttonWithType:UIButtonTypeCustom];
        openButton.frame = CGRectMake((redPacketBg.frame.size.width - 60) / 2, redPacketBg.frame.size.height - 100, 60, 60);
        openButton.backgroundColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.7 alpha:1.0];
        openButton.layer.cornerRadius = 30;
        openButton.layer.borderWidth = 4;
        openButton.layer.borderColor = [UIColor whiteColor].CGColor;
        openButton.layer.shadowColor = [UIColor blackColor].CGColor;
        openButton.layer.shadowOffset = CGSizeMake(0, 2);
        openButton.layer.shadowOpacity = 0.2;
        openButton.layer.shadowRadius = 4;
        [openButton setTitle:@"开" forState:UIControlStateNormal];
        [openButton setTitleColor:[UIColor colorWithRed:0.8 green:0.4 blue:0.2 alpha:1.0] forState:UIControlStateNormal];
        openButton.titleLabel.font = [UIFont boldSystemFontOfSize:24];
        [openButton addTarget:self action:@selector(openRedPacket:) forControlEvents:UIControlEventTouchUpInside];
        [redPacketBg addSubview:openButton];

        // 底部提示文字
        UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, redPacketBg.frame.size.height - 30, redPacketBg.frame.size.width, 20)];
        tipLabel.text = @"拆红包";
        tipLabel.textColor = [UIColor colorWithWhite:1.0 alpha:0.8];
        tipLabel.font = [UIFont systemFontOfSize:13];
        tipLabel.textAlignment = NSTextAlignmentCenter;
        [redPacketBg addSubview:tipLabel];

        self.currentRedPacketView = redPacketView;

        // 关联对象
        objc_setAssociatedObject(openButton, "redPacketView", redPacketView, OBJC_ASSOCIATION_RETAIN_NONATOMIC);

        // 添加到窗口
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:redPacketView];

        // 入场动画
        redPacketView.alpha = 0;
        redPacketView.transform = CGAffineTransformMakeScale(1.1, 1.1);
        [UIView animateWithDuration:0.4 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0.3 options:0 animations:^{
            redPacketView.alpha = 1.0;
            redPacketView.transform = CGAffineTransformIdentity;
        } completion:nil];
    });
}

- (void)openRedPacket:(UIButton *)sender {
    UIView *redPacketView = objc_getAssociatedObject(sender, "redPacketView");

    // 按钮点击动画
    [UIView animateWithDuration:0.1 animations:^{
        sender.transform = CGAffineTransformMakeScale(0.9, 0.9);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.1 animations:^{
            sender.transform = CGAffineTransformIdentity;
        }];
    }];

    // 生成随机金额
    CGFloat amount = [self randomAmount];

    // 切换到全屏结果页面
    [self showRedPacketResultPage:amount];
}

- (void)showRedPacketResultPage:(CGFloat)amount {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 移除当前红包页面
        [self.currentRedPacketView removeFromSuperview];

        // 创建全屏结果页面
        UIView *resultView = [[UIView alloc] initWithFrame:[UIScreen mainScreen].bounds];
        resultView.tag = 999;

        // 上半部分红色背景 - 弧形设计
        UIView *topRedView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, resultView.frame.size.width, resultView.frame.size.height * 0.4)];

        // 创建弧形路径
        UIBezierPath *arcPath = [UIBezierPath bezierPath];
        [arcPath moveToPoint:CGPointMake(0, 0)];
        [arcPath addLineToPoint:CGPointMake(topRedView.frame.size.width, 0)];
        [arcPath addLineToPoint:CGPointMake(topRedView.frame.size.width, topRedView.frame.size.height - 40)];

        // 添加弧形底部
        [arcPath addQuadCurveToPoint:CGPointMake(0, topRedView.frame.size.height - 40)
                        controlPoint:CGPointMake(topRedView.frame.size.width / 2, topRedView.frame.size.height + 20)];
        [arcPath closePath];

        CAShapeLayer *arcLayer = [CAShapeLayer layer];
        arcLayer.path = arcPath.CGPath;
        arcLayer.fillColor = [UIColor colorWithRed:1.0 green:0.4 blue:0.4 alpha:1.0].CGColor;
        [topRedView.layer addSublayer:arcLayer];
        [resultView addSubview:topRedView];

        // 下半部分白色背景
        UIView *bottomWhiteView = [[UIView alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 - 40, resultView.frame.size.width, resultView.frame.size.height * 0.6 + 40)];
        bottomWhiteView.backgroundColor = [UIColor whiteColor];
        [resultView addSubview:bottomWhiteView];

        // 关闭按钮 - 左上角
        UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
        closeButton.frame = CGRectMake(20, 50, 30, 30);
        [closeButton setTitle:@"✕" forState:UIControlStateNormal];
        [closeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        closeButton.titleLabel.font = [UIFont systemFontOfSize:20];
        [closeButton addTarget:self action:@selector(hideRedPacket) forControlEvents:UIControlEventTouchUpInside];
        [topRedView addSubview:closeButton];

        // 更多按钮 - 右上角
        UIButton *moreButton = [UIButton buttonWithType:UIButtonTypeCustom];
        moreButton.frame = CGRectMake(resultView.frame.size.width - 50, 50, 30, 30);
        [moreButton setTitle:@"⋯" forState:UIControlStateNormal];
        [moreButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        moreButton.titleLabel.font = [UIFont boldSystemFontOfSize:20];
        [topRedView addSubview:moreButton];

        // 金币图标 - 居中
        UIView *coinView = [[UIView alloc] initWithFrame:CGRectMake((resultView.frame.size.width - 50) / 2, resultView.frame.size.height * 0.4 - 65, 50, 50)];
        coinView.backgroundColor = [UIColor colorWithRed:1.0 green:0.8 blue:0.0 alpha:1.0];
        coinView.layer.cornerRadius = 25;
        coinView.layer.borderWidth = 3;
        coinView.layer.borderColor = [UIColor colorWithRed:1.0 green:0.9 blue:0.2 alpha:1.0].CGColor;
        coinView.layer.shadowColor = [UIColor blackColor].CGColor;
        coinView.layer.shadowOffset = CGSizeMake(0, 2);
        coinView.layer.shadowOpacity = 0.2;
        coinView.layer.shadowRadius = 4;

        // 在金币中添加 ¥ 符号
        UILabel *coinLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 50, 50)];
        coinLabel.text = @"¥";
        coinLabel.textColor = [UIColor colorWithRed:0.8 green:0.6 blue:0.0 alpha:1.0];
        coinLabel.font = [UIFont boldSystemFontOfSize:20];
        coinLabel.textAlignment = NSTextAlignmentCenter;
        [coinView addSubview:coinLabel];
        [resultView addSubview:coinView];

        // "相信光光光的红包" 标题
        UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 + 20, resultView.frame.size.width, 25)];
        titleLabel.text = @"相信光光光的红包";
        titleLabel.textColor = [UIColor blackColor];
        titleLabel.font = [UIFont systemFontOfSize:16];
        titleLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:titleLabel];

        // "大吉大利" 副标题
        UILabel *subTitleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 + 50, resultView.frame.size.width, 20)];
        subTitleLabel.text = @"大吉大利";
        subTitleLabel.textColor = [UIColor lightGrayColor];
        subTitleLabel.font = [UIFont systemFontOfSize:14];
        subTitleLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:subTitleLabel];

        // 金额显示
        UILabel *amountLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 + 90, resultView.frame.size.width, 50)];
        amountLabel.text = [NSString stringWithFormat:@"¥%.2f", amount];
        amountLabel.textColor = [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0];
        amountLabel.font = [UIFont boldSystemFontOfSize:36];
        amountLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:amountLabel];

        // 提示文字
        UILabel *tipLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 + 150, resultView.frame.size.width, 20)];
        tipLabel.text = @"已存入零钱，可直接消费";
        tipLabel.textColor = [UIColor lightGrayColor];
        tipLabel.font = [UIFont systemFontOfSize:13];
        tipLabel.textAlignment = NSTextAlignmentCenter;
        [bottomWhiteView addSubview:tipLabel];

        // 底部装饰图标区域
        UIView *decorationArea = [[UIView alloc] initWithFrame:CGRectMake(0, resultView.frame.size.height * 0.4 + 200, resultView.frame.size.width, 80)];
        decorationArea.backgroundColor = [UIColor clearColor];

        // 添加三个装饰图标（仿照参考图片）
        NSArray *decorationColors = @[
            [UIColor colorWithRed:1.0 green:0.4 blue:0.4 alpha:1.0],  // 红色
            [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0],  // 橙色
            [UIColor colorWithRed:1.0 green:0.2 blue:0.2 alpha:1.0]   // 深红色
        ];

        for (int i = 0; i < 3; i++) {
            UIView *decorIcon = [[UIView alloc] initWithFrame:CGRectMake(60 + i * 80, 20, 40, 40)];
            decorIcon.backgroundColor = decorationColors[i];
            decorIcon.layer.cornerRadius = 8;
            [decorationArea addSubview:decorIcon];
        }

        [bottomWhiteView addSubview:decorationArea];

        self.currentRedPacketView = resultView;

        // 添加到窗口
        UIWindow *window = [[UIApplication sharedApplication] keyWindow];
        [window addSubview:resultView];

        // 入场动画
        resultView.alpha = 0;
        [UIView animateWithDuration:0.4 animations:^{
            resultView.alpha = 1.0;
        }];

        // 5秒后自动关闭
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self hideRedPacket];
        });
    });
}

- (void)hideRedPacket {
    dispatch_async(dispatch_get_main_queue(), ^{
        UIView *maskView = self.currentRedPacketView;
        if (maskView) {
            [UIView animateWithDuration:0.3 animations:^{
                maskView.alpha = 0;
            } completion:^(BOOL finished) {
                [maskView removeFromSuperview];
                self.currentRedPacketView = nil;
            }];
        }
    });
}

- (UIViewController *)topViewController {
    UIViewController *rootVC = nil;
    UIWindow *window = nil;
    
    if (@available(iOS 15.0, *)) {
        for (UIWindowScene *windowScene in [[UIApplication sharedApplication] connectedScenes]) {
            if ([windowScene isKindOfClass:[UIWindowScene class]]) {
                for (UIWindow *win in windowScene.windows) {
                    if (win.isKeyWindow) {
                        window = win;
                        break;
                    }
                }
                if (window) break;
            }
        }
    } else {
        for (UIWindow *win in [[UIApplication sharedApplication] windows]) {
            if (win.isKeyWindow) {
                window = win;
                break;
            }
        }
    }
    
    if (!window) {
        window = [[UIApplication sharedApplication] windows].firstObject;
    }
    
    rootVC = window.rootViewController;
    while (rootVC.presentedViewController) {
        rootVC = rootVC.presentedViewController;
    }
    
    return rootVC;
}

@end

__attribute__((constructor)) static void entry(void) {
    NSLog(@"RedPacket dylib loaded!");
    dispatch_async(dispatch_get_main_queue(), ^{
        [RedPacketManager sharedInstance];
    });
}
