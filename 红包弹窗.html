<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>红包弹窗</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .red-packet {
            width: 300px;
            height: 500px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        /* 未打开状态 */
        .red-packet.unopened {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #e53e3e 100%);
        }

        .coin-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 50%;
            margin: 40px auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #ff6b6b;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .title {
            text-align: center;
            color: white;
            font-size: 18px;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .blessing {
            text-align: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 80px;
            letter-spacing: 8px;
        }

        .open-button {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #fff, #f5f5f5);
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #8b4513;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s;
            border: 3px solid #ddd;
        }

        .open-button:hover {
            transform: scale(1.05);
        }

        .packet-info {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        /* 已打开状态 */
        .red-packet.opened {
            background: white;
        }

        .top-decoration {
            height: 120px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 50%, #e53e3e 100%);
            border-radius: 0 0 50% 50%;
            position: relative;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .more-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 20px;
            cursor: pointer;
        }

        .opened-content {
            padding: 30px 20px;
            text-align: center;
        }

        .gold-coin {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            color: #ff6b6b;
            font-weight: bold;
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }

        .opened-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .opened-blessing {
            font-size: 14px;
            color: #999;
            margin-bottom: 30px;
        }

        .amount {
            font-size: 48px;
            color: #ff6b6b;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .received-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 40px;
        }

        .share-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
        }

        .bottom-icons {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 20px;
        }

        .icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
        }

        .icon1 {
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            color: white;
        }

        .icon2 {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #ff6b6b;
        }

        .icon3 {
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            color: white;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="modal-overlay">
        <div class="red-packet unopened" id="redPacket">
            <!-- 未打开状态 -->
            <div id="unopenedState">
                <div class="coin-icon">¥</div>
                <div class="title">相信光光的红包</div>
                <div class="blessing">大吉大利</div>
                <div class="open-button" onclick="openRedPacket()">开</div>
                <div class="packet-info">拆红包</div>
            </div>

            <!-- 已打开状态 -->
            <div id="openedState" class="hidden">
                <div class="top-decoration">
                    <div class="close-btn" onclick="closeModal()">×</div>
                    <div class="more-btn">⋯</div>
                </div>
                <div class="opened-content">
                    <div class="gold-coin">¥</div>
                    <div class="opened-title">相信光光的红包</div>
                    <div class="opened-blessing">大吉大利</div>
                    <div class="amount" id="amount">¥1.00</div>
                    <div class="received-text">已存入我的钱包-零钱，去查看 ></div>
                    <div class="share-text">回复个表情，表达感谢</div>
                    <div class="bottom-icons">
                        <div class="icon icon1">🧧</div>
                        <div class="icon icon2">😊</div>
                        <div class="icon icon3">福</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openRedPacket() {
            // 生成10-30之间的随机金额，保留两位小数
            const randomAmount = (Math.random() * 20 + 10).toFixed(2);
            
            // 更新金额显示
            document.getElementById('amount').textContent = `¥${randomAmount}`;
            
            // 切换状态
            const redPacket = document.getElementById('redPacket');
            const unopenedState = document.getElementById('unopenedState');
            const openedState = document.getElementById('openedState');
            
            redPacket.classList.remove('unopened');
            redPacket.classList.add('opened');
            unopenedState.classList.add('hidden');
            openedState.classList.remove('hidden');
        }

        function closeModal() {
            // 关闭弹窗
            document.querySelector('.modal-overlay').style.display = 'none';
        }

        // 点击遮罩层关闭弹窗
        document.querySelector('.modal-overlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
